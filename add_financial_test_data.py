#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from datetime import datetime, date
import random

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'qyf20031211',
    'database': 'goods',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def add_company_financials():
    """添加企业财务数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清空现有数据
        cursor.execute("DELETE FROM company_financials")
        
        # 添加2024年各月份的财务数据
        financial_data = [
            (2024, 1, 2500000.00, 15000000.00, 1.2500),   # 1月
            (2024, 2, 2800000.00, 15200000.00, 1.3200),   # 2月
            (2024, 3, 3200000.00, 15500000.00, 1.4100),   # 3月
            (2024, 4, 2900000.00, 15300000.00, 1.3800),   # 4月
            (2024, 5, 3100000.00, 15600000.00, 1.4000),   # 5月
            (2024, 6, 3300000.00, 15800000.00, 1.4500),   # 6月
            (2024, 7, 3500000.00, 16000000.00, 1.5000),   # 7月（当前月）
            (2024, 8, 3400000.00, 15900000.00, 1.4800),   # 8月
            (2024, 9, 3600000.00, 16100000.00, 1.5200),   # 9月
            (2024, 10, 3800000.00, 16300000.00, 1.5500),  # 10月
            (2024, 11, 3700000.00, 16200000.00, 1.5300),  # 11月
            (2024, 12, 4000000.00, 16500000.00, 1.6000),  # 12月
        ]

        for year, month, cash_flow, total_assets, turnover in financial_data:
            cursor.execute("""
                INSERT INTO company_financials
                (year, month, monthly_operating_cash_flow, total_assets, average_asset_turnover_rate)
                VALUES (%s, %s, %s, %s, %s)
            """, (year, month, cash_flow, total_assets, turnover))
        
        print(f"✅ 成功添加 {len(financial_data)} 条企业财务数据")
        conn.commit()
        
    except Exception as e:
        print(f"❌ 添加企业财务数据失败: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def add_consumable_purchases():
    """添加耗材采购记录"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取耗材列表
        cursor.execute("SELECT id, name, price FROM materials WHERE category = 'consumable'")
        consumables = cursor.fetchall()
        
        if not consumables:
            print("❌ 没有找到耗材数据")
            return
        
        # 获取用户和科室信息
        cursor.execute("SELECT id, department_id FROM users")
        users = cursor.fetchall()

        # 为2024年7月添加耗材采购记录
        purchase_records = []
        total_cost = 0

        for i in range(50):  # 生成50条采购记录
            material = random.choice(consumables)
            user = random.choice(users)
            quantity = random.randint(5, 50)
            unit_price = float(material[2])
            total_price = quantity * unit_price
            total_cost += total_price

            # 随机生成7月的日期
            day = random.randint(1, 31)
            if day > 31:
                day = 31
            purchase_date = f"2024-07-{day:02d}"

            purchase_records.append({
                'material_id': material[0],
                'material_name': material[1],
                'user_id': user[0],
                'department_id': user[1],
                'quantity': quantity,
                'unit_price': unit_price,
                'total_price': total_price,
                'purchase_date': purchase_date
            })

        # 插入采购记录到material_requests表（模拟已批准的申请）
        for record in purchase_records:
            cursor.execute("""
                INSERT INTO material_requests
                (user_id, material_id, department_id, quantity, reason, status, request_date, processed_date)
                VALUES (%s, %s, %s, %s, %s, 'approved', %s, %s)
            """, (
                record['user_id'],
                record['material_id'],
                record['department_id'],
                record['quantity'],
                f"采购{record['material_name']}",
                record['purchase_date'],
                record['purchase_date']
            ))
        
        print(f"✅ 成功添加 {len(purchase_records)} 条耗材采购记录")
        print(f"📊 7月份耗材采购总金额: ¥{total_cost:,.2f}")
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ 添加耗材采购记录失败: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def update_asset_usage_data():
    """更新固定资产使用数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取固定资产列表
        cursor.execute("SELECT id, name FROM materials WHERE category = 'fixed_asset'")
        assets = cursor.fetchall()
        
        if not assets:
            print("❌ 没有找到固定资产数据")
            return
        
        updated_count = 0
        for asset in assets:
            # 随机生成使用数据
            usage_count = random.randint(10, 200)  # 使用次数
            total_usage_hours = random.uniform(100, 2000)  # 使用时长（小时）
            
            cursor.execute("""
                UPDATE materials 
                SET usage_count = %s, total_usage_hours = %s
                WHERE id = %s
            """, (usage_count, total_usage_hours, asset[0]))
            
            updated_count += 1
        
        print(f"✅ 成功更新 {updated_count} 个固定资产的使用数据")
        conn.commit()
        
    except Exception as e:
        print(f"❌ 更新固定资产使用数据失败: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def verify_data():
    """验证数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查企业财务数据
        cursor.execute("SELECT COUNT(*) as count FROM company_financials")
        financial_count = cursor.fetchone()[0]
        print(f"📊 企业财务数据记录数: {financial_count}")
        
        # 检查7月份现金流
        cursor.execute("""
            SELECT monthly_operating_cash_flow 
            FROM company_financials 
            WHERE year = 2024 AND month = 7
        """)
        july_cash_flow = cursor.fetchone()
        if july_cash_flow:
            print(f"💰 2024年7月经营现金流: ¥{july_cash_flow[0]:,.2f}")
        
        # 检查耗材采购记录
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE m.category = 'consumable'
            AND mr.status = 'approved'
            AND YEAR(mr.processed_date) = 2024
            AND MONTH(mr.processed_date) = 7
        """)
        consumable_count = cursor.fetchone()[0]
        print(f"🛒 7月份耗材采购记录数: {consumable_count}")

        # 计算7月份耗材采购总金额
        cursor.execute("""
            SELECT SUM(m.price * mr.quantity) as total_cost
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE m.category = 'consumable'
            AND mr.status = 'approved'
            AND YEAR(mr.processed_date) = 2024
            AND MONTH(mr.processed_date) = 7
        """)
        total_cost_result = cursor.fetchone()
        if total_cost_result and total_cost_result[0]:
            print(f"💰 7月份耗材采购总金额: ¥{total_cost_result[0]:,.2f}")
        
        # 检查固定资产使用数据
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM materials 
            WHERE category = 'fixed_asset' 
            AND usage_count > 0
        """)
        asset_usage_count = cursor.fetchone()[0]
        print(f"🔧 有使用数据的固定资产数: {asset_usage_count}")
        
    except Exception as e:
        print(f"❌ 验证数据失败: {e}")
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数"""
    print("🚀 开始添加金融测试数据...")
    print("=" * 50)
    
    # 1. 添加企业财务数据
    print("1. 添加企业财务数据...")
    add_company_financials()
    
    # 2. 添加耗材采购记录
    print("\n2. 添加耗材采购记录...")
    add_consumable_purchases()
    
    # 3. 更新固定资产使用数据
    print("\n3. 更新固定资产使用数据...")
    update_asset_usage_data()
    
    # 4. 验证数据
    print("\n4. 验证数据...")
    verify_data()
    
    print("\n" + "=" * 50)
    print("✅ 金融测试数据添加完成！")

if __name__ == "__main__":
    main()
